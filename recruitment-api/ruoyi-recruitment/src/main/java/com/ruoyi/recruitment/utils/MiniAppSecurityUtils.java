package com.ruoyi.recruitment.utils;

import com.ruoyi.recruitment.domain.RecJobSeeker;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 小程序安全工具类
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class MiniAppSecurityUtils
{
    /**
     * 获取当前登录的小程序用户
     * 
     * @return 当前用户信息
     */
    public static RecJobSeeker getCurrentSeeker()
    {
        HttpServletRequest request = getRequest();
        if (request != null)
        {
            return (RecJobSeeker) request.getAttribute("currentSeeker");
        }
        return null;
    }
    
    /**
     * 获取当前登录用户的ID
     * 
     * @return 用户ID
     */
    public static Long getCurrentSeekerId()
    {
        HttpServletRequest request = getRequest();
        if (request != null)
        {
            return (Long) request.getAttribute("currentSeekerId");
        }
        return null;
    }
    
    /**
     * 获取当前请求
     * 
     * @return HttpServletRequest
     */
    private static HttpServletRequest getRequest()
    {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null)
        {
            return attributes.getRequest();
        }
        return null;
    }
    
    /**
     * 检查当前用户是否已登录
     * 
     * @return 是否已登录
     */
    public static boolean isAuthenticated()
    {
        return getCurrentSeeker() != null;
    }
    
    /**
     * 检查当前用户是否为指定用户
     * 
     * @param seekerId 用户ID
     * @return 是否为指定用户
     */
    public static boolean isCurrentUser(Long seekerId)
    {
        Long currentSeekerId = getCurrentSeekerId();
        return currentSeekerId != null && currentSeekerId.equals(seekerId);
    }
}
