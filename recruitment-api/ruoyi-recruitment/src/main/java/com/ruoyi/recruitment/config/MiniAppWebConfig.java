package com.ruoyi.recruitment.config;

import com.ruoyi.recruitment.interceptor.MiniAppTokenInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 小程序Web配置
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Configuration
public class MiniAppWebConfig implements WebMvcConfigurer
{
    @Autowired
    private MiniAppTokenInterceptor miniAppTokenInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        // 添加小程序token验证拦截器
        registry.addInterceptor(miniAppTokenInterceptor)
                .addPathPatterns("/miniapp/**")  // 拦截所有小程序接口
                .excludePathPatterns(
                    "/miniapp/auth/**",          // 排除认证相关接口
                    "/miniapp/public/**"         // 排除公共接口
                );
    }
}
