package com.ruoyi.recruitment.service;

import com.ruoyi.recruitment.domain.RecJobSeeker;

/**
 * 小程序Token服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface MiniAppTokenService 
{
    /**
     * 创建小程序用户token
     * 
     * @param seeker 求职者信息
     * @return token
     */
    String createToken(RecJobSeeker seeker);
    
    /**
     * 验证token并获取用户信息
     * 
     * @param token token
     * @return 求职者信息
     */
    RecJobSeeker getJobSeekerByToken(String token);
    
    /**
     * 刷新token
     * 
     * @param token 原token
     * @return 新token
     */
    String refreshToken(String token);
    
    /**
     * 删除token
     * 
     * @param token token
     */
    void deleteToken(String token);
    
    /**
     * 验证token是否有效
     * 
     * @param token token
     * @return 是否有效
     */
    boolean validateToken(String token);
}
