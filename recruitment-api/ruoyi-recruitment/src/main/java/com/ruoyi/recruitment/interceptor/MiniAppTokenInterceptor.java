package com.ruoyi.recruitment.interceptor;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.recruitment.domain.RecJobSeeker;
import com.ruoyi.recruitment.service.MiniAppTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 小程序Token验证拦截器
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
public class MiniAppTokenInterceptor implements HandlerInterceptor
{
    private static final Logger log = LoggerFactory.getLogger(MiniAppTokenInterceptor.class);
    
    @Autowired
    private MiniAppTokenService miniAppTokenService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        // 获取token
        String token = getToken(request);
        
        if (StringUtils.isEmpty(token))
        {
            renderError(response, "未提供认证token");
            return false;
        }
        
        // 验证token并获取用户信息
        RecJobSeeker seeker = miniAppTokenService.getJobSeekerByToken(token);
        
        if (seeker == null)
        {
            renderError(response, "token无效或已过期");
            return false;
        }
        
        // 将用户信息存储到request中，供后续使用
        request.setAttribute("currentSeeker", seeker);
        request.setAttribute("currentSeekerId", seeker.getSeekerId());
        
        log.debug("小程序用户认证成功，用户ID: {}", seeker.getSeekerId());
        
        return true;
    }
    
    /**
     * 获取请求中的token
     */
    private String getToken(HttpServletRequest request)
    {
        // 优先从Header中获取
        String token = request.getHeader("Authorization");
        if (StringUtils.isNotEmpty(token) && token.startsWith("Bearer "))
        {
            return token.substring(7);
        }
        
        // 从Header中获取（不带Bearer前缀）
        token = request.getHeader("token");
        if (StringUtils.isNotEmpty(token))
        {
            return token;
        }
        
        // 从参数中获取
        token = request.getParameter("token");
        if (StringUtils.isNotEmpty(token))
        {
            return token;
        }
        
        return null;
    }
    
    /**
     * 渲染错误信息
     */
    private void renderError(HttpServletResponse response, String message) throws IOException
    {
        response.setStatus(401);
        response.setContentType("application/json;charset=utf-8");
        
        AjaxResult result = AjaxResult.error(401, message);
        response.getWriter().write(JSON.toJSONString(result));
    }
}
