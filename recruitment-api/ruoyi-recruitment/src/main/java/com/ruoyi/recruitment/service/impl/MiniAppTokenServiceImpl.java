package com.ruoyi.recruitment.service.impl;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.recruitment.domain.RecJobSeeker;
import com.ruoyi.recruitment.service.IRecJobSeekerService;
import com.ruoyi.recruitment.service.MiniAppTokenService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 小程序Token服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class MiniAppTokenServiceImpl implements MiniAppTokenService
{
    private static final Logger log = LoggerFactory.getLogger(MiniAppTokenServiceImpl.class);
    
    // 小程序token前缀
    private static final String MINIAPP_TOKEN_KEY = "miniapp_token:";
    
    // 小程序用户信息前缀
    private static final String MINIAPP_USER_KEY = "miniapp_user:";
    
    // token有效期（默认7天）
    private static final int EXPIRE_TIME = 7 * 24 * 60;
    
    // 毫秒
    protected static final long MILLIS_SECOND = 1000;
    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private IRecJobSeekerService recJobSeekerService;
    
    // 令牌密钥
    @Value("${token.secret}")
    private String secret;
    
    /**
     * 创建小程序用户token
     */
    @Override
    public String createToken(RecJobSeeker seeker)
    {
        String tokenId = IdUtils.fastUUID();
        
        // 构建JWT claims
        Map<String, Object> claims = new HashMap<>();
        claims.put("tokenId", tokenId);
        claims.put("seekerId", seeker.getSeekerId());
        claims.put("phone", seeker.getPhone());
        claims.put("createTime", System.currentTimeMillis());
        
        // 生成JWT token
        String jwtToken = createJwtToken(claims);
        
        // 将用户信息存储到Redis中，使用tokenId作为key
        String userKey = MINIAPP_USER_KEY + tokenId;
        redisCache.setCacheObject(userKey, seeker, EXPIRE_TIME, TimeUnit.MINUTES);
        
        // 将token映射存储到Redis中
        String tokenKey = MINIAPP_TOKEN_KEY + tokenId;
        redisCache.setCacheObject(tokenKey, jwtToken, EXPIRE_TIME, TimeUnit.MINUTES);
        
        log.info("为用户{}创建token成功，tokenId: {}", seeker.getSeekerId(), tokenId);
        
        return jwtToken;
    }
    
    /**
     * 验证token并获取用户信息
     */
    @Override
    public RecJobSeeker getJobSeekerByToken(String token)
    {
        if (StringUtils.isEmpty(token))
        {
            return null;
        }
        
        try
        {
            // 解析JWT token
            Claims claims = parseJwtToken(token);
            String tokenId = (String) claims.get("tokenId");
            
            if (StringUtils.isEmpty(tokenId))
            {
                return null;
            }
            
            // 从Redis中获取用户信息
            String userKey = MINIAPP_USER_KEY + tokenId;
            RecJobSeeker seeker = redisCache.getCacheObject(userKey);
            
            if (seeker != null)
            {
                // 验证token是否匹配
                String tokenKey = MINIAPP_TOKEN_KEY + tokenId;
                String cachedToken = redisCache.getCacheObject(tokenKey);
                
                if (token.equals(cachedToken))
                {
                    // 自动续期（如果剩余时间少于1天，则续期）
                    Long expire = redisCache.getExpire(userKey);
                    if (expire != null && expire < 24 * 60 * 60)
                    {
                        redisCache.expire(userKey, EXPIRE_TIME, TimeUnit.MINUTES);
                        redisCache.expire(tokenKey, EXPIRE_TIME, TimeUnit.MINUTES);
                        log.info("为用户{}自动续期token，tokenId: {}", seeker.getSeekerId(), tokenId);
                    }
                    
                    return seeker;
                }
            }
        }
        catch (Exception e)
        {
            log.error("解析token失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 刷新token
     */
    @Override
    public String refreshToken(String token)
    {
        RecJobSeeker seeker = getJobSeekerByToken(token);
        if (seeker != null)
        {
            // 删除旧token
            deleteToken(token);
            // 创建新token
            return createToken(seeker);
        }
        return null;
    }
    
    /**
     * 删除token
     */
    @Override
    public void deleteToken(String token)
    {
        if (StringUtils.isEmpty(token))
        {
            return;
        }
        
        try
        {
            Claims claims = parseJwtToken(token);
            String tokenId = (String) claims.get("tokenId");
            
            if (StringUtils.isNotEmpty(tokenId))
            {
                String userKey = MINIAPP_USER_KEY + tokenId;
                String tokenKey = MINIAPP_TOKEN_KEY + tokenId;
                
                redisCache.deleteObject(userKey);
                redisCache.deleteObject(tokenKey);
                
                log.info("删除token成功，tokenId: {}", tokenId);
            }
        }
        catch (Exception e)
        {
            log.error("删除token失败: {}", e.getMessage());
        }
    }
    
    /**
     * 验证token是否有效
     */
    @Override
    public boolean validateToken(String token)
    {
        return getJobSeekerByToken(token) != null;
    }
    
    /**
     * 创建JWT token
     */
    private String createJwtToken(Map<String, Object> claims)
    {
        return Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }
    
    /**
     * 解析JWT token
     */
    private Claims parseJwtToken(String token)
    {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }
}
